/**
 * Simple in-memory cache implementation for performance optimization
 */

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

class MemoryCache {
  private cache = new Map<string, CacheItem<any>>();
  private maxSize: number;
  private defaultTTL: number;

  constructor(maxSize: number = 1000, defaultTTL: number = 300000) { // 5 minutes default
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL;
    
    // Clean up expired items every minute
    setInterval(() => this.cleanup(), 60000);
  }

  /**
   * Set a value in the cache
   */
  set<T>(key: string, value: T, ttl?: number): void {
    // Remove oldest items if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      data: value,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    });
  }

  /**
   * Get a value from the cache
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // Check if item has expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  /**
   * Check if a key exists in the cache
   */
  has(key: string): boolean {
    const item = this.cache.get(key);
    
    if (!item) {
      return false;
    }

    // Check if item has expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Delete a key from the cache
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all items from the cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.calculateHitRate()
    };
  }

  /**
   * Clean up expired items
   */
  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    
    if (keysToDelete.length > 0) {
      console.log(`Cache cleanup: removed ${keysToDelete.length} expired items`);
    }
  }

  /**
   * Calculate cache hit rate (simplified)
   */
  private calculateHitRate(): number {
    // This is a simplified implementation
    // In a real scenario, you'd track hits and misses
    return this.cache.size / this.maxSize;
  }
}

// Create global cache instance
export const cache = new MemoryCache();

/**
 * Cache decorator for functions
 */
export function cached<T extends (...args: any[]) => any>(
  ttl: number = 300000, // 5 minutes default
  keyGenerator?: (...args: Parameters<T>) => string
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: Parameters<T>) {
      const cacheKey = keyGenerator 
        ? keyGenerator(...args)
        : `${target.constructor.name}.${propertyName}:${JSON.stringify(args)}`;

      // Try to get from cache first
      const cachedResult = cache.get<ReturnType<T>>(cacheKey);
      if (cachedResult !== null) {
        console.log(`Cache hit: ${cacheKey}`);
        return cachedResult;
      }

      // Execute the original method
      console.log(`Cache miss: ${cacheKey}`);
      const result = await method.apply(this, args);

      // Store result in cache
      cache.set(cacheKey, result, ttl);

      return result;
    };

    return descriptor;
  };
}

/**
 * Cache utility functions
 */
export const cacheUtils = {
  /**
   * Get or set a value in the cache
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttl?: number
  ): Promise<T> {
    const cached = cache.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const value = await factory();
    cache.set(key, value, ttl);
    return value;
  },

  /**
   * Invalidate cache entries by pattern
   */
  invalidatePattern(pattern: string): number {
    let count = 0;
    const regex = new RegExp(pattern);
    
    for (const key of cache['cache'].keys()) {
      if (regex.test(key)) {
        cache.delete(key);
        count++;
      }
    }
    
    console.log(`Invalidated ${count} cache entries matching pattern: ${pattern}`);
    return count;
  },

  /**
   * Warm up cache with commonly accessed data
   */
  async warmUp(warmUpFunctions: Array<() => Promise<void>>): Promise<void> {
    console.log('Warming up cache...');

    try {
      await Promise.all(warmUpFunctions.map(fn => fn()));
      console.log('Cache warm-up completed');
    } catch (error) {
      console.error('Cache warm-up failed:', error);
    }
  },

  /**
   * Clear all user-specific cache entries
   */
  clearUserCache(username: string): number {
    let count = 0;
    const keysToDelete: string[] = [];

    // Get all cache keys
    for (const key of cache['cache'].keys()) {
      // Check if key contains the username
      if (key.includes(username)) {
        keysToDelete.push(key);
      }
    }

    // Delete all matching keys
    keysToDelete.forEach(key => {
      if (cache.delete(key)) {
        count++;
      }
    });

    console.log(`Cleared ${count} cache entries for user: ${username}`);
    return count;
  }
};

/**
 * Cache middleware for Express routes
 */
export function cacheMiddleware(ttl: number = 300000) {
  return (req: any, res: any, next: any) => {
    const key = `route:${req.method}:${req.originalUrl}`;
    const cached = cache.get(key);

    if (cached) {
      console.log(`Route cache hit: ${key}`);
      return res.json(cached);
    }

    // Override res.json to cache the response
    const originalJson = res.json;
    res.json = function (data: any) {
      cache.set(key, data, ttl);
      console.log(`Route cache set: ${key}`);
      return originalJson.call(this, data);
    };

    next();
  };
}

/**
 * Cache keys for different data types
 */
export const CACHE_KEYS = {
  SENSOR_DATA: (sensorId: string) => `sensor_data:${sensorId}`,
  SENSOR_LIST: (userId: string) => `sensor_list:${userId}`,
  USER_PERMISSIONS: (userId: string) => `user_permissions:${userId}`,
  API_TOKEN: 'api_token:boqucloud',
  SYSTEM_SETTINGS: 'system_settings',
  USER_PROFILE: (userId: string) => `user_profile:${userId}`
};

/**
 * Cache TTL constants (in milliseconds)
 */
export const CACHE_TTL = {
  SENSOR_DATA: 60 * 1000,      // 1 minute
  SENSOR_LIST: 5 * 60 * 1000,  // 5 minutes
  USER_PERMISSIONS: 10 * 60 * 1000, // 10 minutes
  API_TOKEN: 50 * 60 * 1000,   // 50 minutes (tokens expire in 1 hour)
  SYSTEM_SETTINGS: 60 * 60 * 1000, // 1 hour
  USER_PROFILE: 15 * 60 * 1000 // 15 minutes
};
