import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { FileText, MapPin, Signal, Droplet, Wind, Activity, Thermometer, Zap, Anchor, Cloud } from 'lucide-react';
import { useApiOptimization } from '../../hooks/useApiOptimization';
import { useSmartPolling } from '../../hooks/useSmartPolling';
import { optimizedApiService } from '../../services/optimizedApiService';
import SensorChart from './components/SensorChart';
import HistoricalReportDialog from './components/HistoricalReportDialog';
import FetchDataDialog from './components/FetchDataDialog';
import ServerDataDialog from './components/ServerDataDialog';
import { useAuth } from '../../context/AuthContext';
import TimeFrameToggle, { TimeFrame } from './components/TimeFrameToggle';
import { Header } from '../shared/Header';
import { formatDateTime } from '../../utils/dateFormat';
import PerformanceMonitor from '../shared/PerformanceMonitor';
import { checkSensorPermission } from '../../utils/permissionUtils';
import { api } from '../../api/client';
import { OptimizedSensorData, SensorDetails, SensorReading, SignalData } from '../../types';
import { getSensorSignalData, getSensorReadings as getLocalSensorReadings } from './data/sensorData'; // Local CSV data only
import { getLatestLocalSensorFallbackData, LatestLocalSensorData } from '../../utils/sensorDataUtils';
import { parse } from 'date-fns'; // For parsing API timestamp string if needed for comparison

// Interface defining the structure of sensor data
interface SensorDataType {
  salinity: number;
  do: number;
  ph: number;
  temp: number;
  cond: number;
  depth: number;
  turbidity: number;
  latitude: number;
  longitude: number;
  timestamp: string;
}

// Interface for sensor readings
interface SensorReading {
  value: number;
  timestamp: string;
}

// Interface for signal reading
interface SignalReading {
  status: string;
  signalLevel: number;
}

// Interface for sensor details
interface Sensor {
  equipmentId: string;
  apiId: string;
  name: string;
  location: string;
  status: string;
}

/**
 * OptimizedSensorDetailPage Component
 *
 * This component maintains complete visual consistency with docs/SD.tsx by implementing:
 * 1. **Graph Layouts**: Line graph format with identical chart dimensions, axis styling, and color schemes
 * 2. **Card Layout Structure**: Exact card component layout with proper padding, margins, and grid system
 * 3. **Typography and Fonts**: Same font family, weights, sizes, and heading hierarchy
 * 4. **Visual Styling**: Identical color scheme, button styles, spacing units, and loading states
 * 5. **Timezone Adjustment**: All graphs display time reduced by four hours as specified
 * 6. **Grid Layouts**: Consistent responsive breakpoints and spacing
 *
 * The component wraps each chart in individual cards with 'bg-white rounded-lg shadow-md p-6'
 * to match the exact styling patterns from the reference design.
 */
function OptimizedSensorDetailPage() {
  const navigate = useNavigate();
  const { id } = useParams();
  const { getCurrentUser } = useAuth();
  const { optimizedCall, invalidateCache, getCacheStats } = useApiOptimization();

  // State management - matching target design
  const [sensor, setSensor] = useState<Sensor | null>(null);
  const [sensorData, setSensorData] = useState<SensorDataType | null>(null);
  const [readings, setReadings] = useState<Record<string, SensorReading[]>>({});
  const [rawReadings, setRawReadings] = useState<Record<string, SensorReading[]>>({}); // Cache for all raw data
  const [signalData, setSignalData] = useState<SignalReading | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingTimeframe, setIsLoadingTimeframe] = useState(false); // Loading state for timeframe changes
  const [error, setError] = useState<string | null>(null);
  const [timeFrame, setTimeFrame] = useState<TimeFrame>('1d');
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [showFetchDialog, setShowFetchDialog] = useState(false);
  const [showServerDataDialog, setShowServerDataDialog] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [hasPermission, setHasPermission] = useState(false);
  const [sensorName, setSensorName] = useState('');
  const [refId, setRefId] = useState('');
  const [apiId, setApiId] = useState('');
  const [userFullName, setUserFullName] = useState('');
  const [isFetching, setIsFetching] = useState(false);
  const [isFetchingData, setIsFetchingData] = useState(false);
  const [isFetchingServerData, setIsFetchingServerData] = useState(false);
  const [serverData, setServerData] = useState(null);
  const [isLoadingServerData, setIsLoadingServerData] = useState(false);
  const [serverDataError, setServerDataError] = useState(null);
  const [isUsingFallbackData, setIsUsingFallbackData] = useState(false);
  const [isUpdatingData, setIsUpdatingData] = useState(false);
  const [isUpdatingSignal, setIsUpdatingSignal] = useState(false);
  const [isUpdatingReadings, setIsUpdatingReadings] = useState(false);
  const cacheStats = getCacheStats();

  /**
   * Optimized sensor details fetching with fallback to local data
   */
  const fetchSensorDetails = useCallback(async () => {
    if (!id) return null;

    try {
      // Try API first
      const sensorDetails = await optimizedCall(
        `sensor:details:${id}`,
        () => optimizedApiService.getSensorDetails(id),
        {
          ttl: 30 * 60 * 1000, // 30 minutes cache
          staleWhileRevalidate: true // Return stale data immediately while fetching fresh
        }
      );
      return sensorDetails;
    } catch (err) {
      console.warn('API failed to load sensor details:', err);

      // No fallback to hardcoded data - show error instead
      throw new Error('Failed to load sensor details from API');
    }
  }, [id, optimizedCall]);

  /**
   * Optimized sensor data fetching with fallback to local data
   */
  const fetchSensorData = useCallback(async (apiId: string) => {
    try {
      // Try API first
      const data = await optimizedCall(
        `sensor:data:${apiId}`,
        () => optimizedApiService.getSensorData(apiId),
        {
          ttl: 30 * 60 * 1000, // 30 minutes cache to match polling interval
          staleWhileRevalidate: true // Return stale data while fetching fresh
        }
      );

      // Map API ID to equipment ID for local data lookup
      // This is necessary because local data files are named by equipment ID (e.g., NB001.ts)
      // but the API uses different IDs (e.g., 22063)
      let equipmentId = apiId;
      if (apiId === '22063') {
        equipmentId = 'NB001'; // Map API ID 22063 to equipment ID NB001
      }
      // Add more mappings as needed for other sensors

      // Fetch latest local fallback data regardless of API success to compare timestamps
      const latestLocalData = await getLatestLocalSensorFallbackData(equipmentId);

      // Scenario 1: API succeeded and returned data
      if (data) {
        if (latestLocalData) {
          // Both API and local data are available, compare timestamps
          // API timestamp is DD/MM/YYYY HH:mm (after processApiTimestamp)
          // Local data timestamp is DD/MM/YYYY HH:mm (from CSV, dateTimeObject is Date object)
          const apiTimestampDate = parse(data.timestamp, 'dd/MM/yyyy HH:mm', new Date());

          if (isNaN(apiTimestampDate.getTime())) {
            console.warn(`Failed to parse API timestamp for comparison: ${data.timestamp}. Using API data by default as local data might be stale or also problematic.`);
            setIsUsingFallbackData(false); // Assuming API data is preferred if its timestamp is bad
            return data;
          }

          console.log(`📊 Timestamp comparison for sensor ${apiId}:`);
          console.log(`   API timestamp: ${data.timestamp} (parsed: ${apiTimestampDate})`);
          console.log(`   Local timestamp: ${latestLocalData.timestamp} (parsed: ${latestLocalData.dateTimeObject})`);
          console.log(`   Local is newer: ${latestLocalData.dateTimeObject > apiTimestampDate}`);

          if (latestLocalData.dateTimeObject > apiTimestampDate) {
            console.log(`🔄 Using latest local fallback data for sensor ${apiId} as it's newer. Local: ${latestLocalData.timestamp}, API: ${data.timestamp}`);
            setIsUsingFallbackData(true);
            return {
              salinity: latestLocalData.salinity,
              do: latestLocalData.do,
              ph: latestLocalData.ph,
              temp: latestLocalData.temp,
              cond: latestLocalData.cond,
              depth: latestLocalData.depth,
              turbidity: latestLocalData.turbidity,
              latitude: latestLocalData.latitude,
              longitude: latestLocalData.longitude,
              timestamp: latestLocalData.timestamp,
            };
          } else {
            console.log(`🔄 Using API data for sensor ${apiId} as it's newer or same. API: ${data.timestamp}, Local: ${latestLocalData.timestamp}`);
            setIsUsingFallbackData(false);
            return data;
          }
        } else {
          // API succeeded, but no local data available for comparison
          console.log(`🔄 Using API data for sensor ${apiId} (no local data for comparison).`);
          setIsUsingFallbackData(false);
          return data;
        }
      } 
      // Scenario 2: API failed (data is null), but local data is available
      else if (latestLocalData) {
        console.log(`🔄 API data failed or returned null, using latest local fallback data for sensor ${apiId}. Timestamp: ${latestLocalData.timestamp}`);
        setIsUsingFallbackData(true);
        return {
            salinity: latestLocalData.salinity,
            do: latestLocalData.do,
            ph: latestLocalData.ph,
            temp: latestLocalData.temp,
            cond: latestLocalData.cond,
            depth: latestLocalData.depth,
            turbidity: latestLocalData.turbidity,
            latitude: latestLocalData.latitude,
            longitude: latestLocalData.longitude,
            timestamp: latestLocalData.timestamp,
        };
      }
      // Scenario 3: Both API and specific local fallback data failed or are unavailable
      else {
        console.warn(`API call for sensor:data:${apiId} returned null, and no local CSV data available.`);
        // No fallback to hardcoded data - return null to indicate no data available
        return null;
      }
    } catch (err) { // This catch block handles errors from optimizedCall or getLatestLocalSensorFallbackData
      console.warn('Error during sensor data fetching or primary fallback, attempting further fallbacks:', err);
      // Attempt to use latest local data if not already tried or if the error was from API part
      try {
        const latestLocalDataOnError = await getLatestLocalSensorFallbackData(apiId);
        if (latestLocalDataOnError) {
            console.log(`🔄 Using latest local fallback data for sensor ${apiId} due to error in primary fetch. Timestamp: ${latestLocalDataOnError.timestamp}`);
            setIsUsingFallbackData(true);
            return {
                salinity: latestLocalDataOnError.salinity,
                do: latestLocalDataOnError.do,
                ph: latestLocalDataOnError.ph,
                temp: latestLocalDataOnError.temp,
                cond: latestLocalDataOnError.cond,
                depth: latestLocalDataOnError.depth,
                turbidity: latestLocalDataOnError.turbidity,
                latitude: latestLocalDataOnError.latitude,
                longitude: latestLocalDataOnError.longitude,
                timestamp: latestLocalDataOnError.timestamp,
            };
        }
      } catch (localFallbackError) {
          console.warn('Error fetching latest local data during error handling:', localFallbackError);
      }

      // No fallback to hardcoded data - return null to indicate no data available
      console.warn(`Critical error in fetchSensorData for ${apiId}, and no local CSV data available.`);
      return null;
    } // This closes the catch (err) block from line 256
    // The erroneous extra catch block that was here has been removed.
  }, [optimizedCall, setIsUsingFallbackData]); // Added setIsUsingFallbackData to dependencies

  /**
   * Optimized signal data fetching with fallback to local data
   */
  const fetchSignalData = useCallback(async (sensorId: string) => {
    try {
      // Try API first
      const signal = await optimizedCall(
        `sensor:signal:${sensorId}`,
        () => optimizedApiService.getSensorSignalData(sensorId),
        {
          ttl: 30 * 60 * 1000, // 30 minutes cache for signal data
          staleWhileRevalidate: true
        }
      );
      return signal;
    } catch (err) {
      console.warn('API failed, using fallback data for signal data:', err);

      // Fallback to local data
      try {
        const signalData = getSensorSignalData(sensorId);
        return signalData;
      } catch (fallbackError) {
        console.warn('Fallback signal data also failed:', fallbackError);
        return null; // Return null if both API and fallback fail
      }
    }
  }, [optimizedCall, optimizedApiService]);

  /**
   * Optimized readings fetching with smart caching and filtering
   */
  const fetchSensorReadings = useCallback(async (sensorId: string, forceRefresh: boolean = false) => {
    try {
      const parameters = ['salinity', 'do', 'ph', 'temp', 'cond', 'depth', 'turbidity'];

      // Check if we already have raw data cached (without timeframe in cache key)
      let rawData = rawReadings;

      // Only fetch if we don't have cached data or force refresh
      if (Object.keys(rawData).length === 0 || forceRefresh) {
        console.log('🔄 Fetching fresh sensor readings data...');

        // Fetch all readings in parallel without timeframe dependency
        const readingsPromises = parameters.map(param =>
          optimizedCall(
            `sensor:readings:${sensorId}:${param}:raw`,
            () => optimizedApiService.getSensorReadings(sensorId, param),
            {
              ttl: 30 * 60 * 1000, // 30 minutes cache for raw readings
              staleWhileRevalidate: true
            }
          ).catch(error => {
            console.warn(`Failed to fetch ${param} readings from API, trying fallback:`, error);

            // Fallback to local data for this parameter
            try {
              return getLocalSensorReadings(sensorId, param);
            } catch (fallbackError) {
              console.warn(`Fallback also failed for ${param}:`, fallbackError);
              return []; // Return empty array if both API and fallback fail
            }
          })
        );

        const readingsResults = await Promise.all(readingsPromises);

        // Store raw data in state cache
        const newRawData: Record<string, SensorReading[]> = {};
        parameters.forEach((param, index) => {
          newRawData[param] = readingsResults[index] || [];
        });

        setRawReadings(newRawData);
        rawData = newRawData;
      } else {
        console.log('📦 Using cached sensor readings data');
      }

      return rawData;
    } catch (err) {
      console.error('Failed to fetch sensor readings:', err);
      return {};
    }
  }, [optimizedCall, rawReadings]);

  /**
   * Fast client-side data filtering by timeframe
   * Uses the latest data timestamp as reference instead of current system date
   */
  const filterDataByTimeframe = useCallback((data: Record<string, SensorReading[]>, timeFrame: TimeFrame): Record<string, SensorReading[]> => {
    if (!data || Object.keys(data).length === 0) return {};

    // Find the latest timestamp across all parameters
    let latestTimestamp = new Date(0); // Start with epoch
    Object.values(data).forEach(readings => {
      if (readings && readings.length > 0) {
        readings.forEach(reading => {
          const readingDate = new Date(reading.timestamp);
          if (readingDate > latestTimestamp) {
            latestTimestamp = readingDate;
          }
        });
      }
    });

    // If no data found, use current date as fallback
    if (latestTimestamp.getTime() === 0) {
      latestTimestamp = new Date();
    }

    const timeFrameInDays = timeFrame === '1d' ? 1 : timeFrame === '7d' ? 7 : 30;
    const startTime = new Date(latestTimestamp.getTime() - (timeFrameInDays * 24 * 60 * 60 * 1000));

    const filteredData: Record<string, SensorReading[]> = {};

    Object.entries(data).forEach(([param, readings]) => {
      if (readings && readings.length > 0) {
        // Sort by timestamp and filter by timeframe
        const sortedReadings = readings
          .filter(reading => {
            const readingDate = new Date(reading.timestamp);
            return readingDate >= startTime && readingDate <= latestTimestamp;
          })
          .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

        filteredData[param] = sortedReadings;
      } else {
        filteredData[param] = [];
      }
    });

    return filteredData;
  }, []);

  /**
   * Main data loading function with optimized parallel fetching and progressive updates
   */
  const loadAllData = useCallback(async () => {
    if (!id) return;

    try {
      if (!sensorData && !signalData) {
        setIsLoading(true); // Only show global loading if no data is displayed
      }
      setError(null);

      // Step 1: Load sensor details first (fastest, usually cached)
      const sensorDetails = await fetchSensorDetails();
      if (sensorDetails) {
        setSensor(sensorDetails);

        // Step 2: Load all other data in parallel with individual loading states
        // Note: fetchSensorData uses apiId, but fetchSignalData and fetchSensorReadings use equipmentId
        // This is because the API endpoints use apiId, but local data files use equipmentId

        // Set individual loading states
        setIsUpdatingData(true);
        setIsUpdatingSignal(true);
        setIsUpdatingReadings(true);

        const [sensorDataResult, signalDataResult, readingsDataResult] = await Promise.allSettled([
          fetchSensorData(sensorDetails.apiId).finally(() => setIsUpdatingData(false)),
          fetchSignalData(sensorDetails.equipmentId).finally(() => setIsUpdatingSignal(false)),
          fetchSensorReadings(sensorDetails.equipmentId).finally(() => setIsUpdatingReadings(false))
        ]);

        // Update state with successful results
        if (sensorDataResult.status === 'fulfilled' && sensorDataResult.value) {
          setSensorData(sensorDataResult.value);
          console.log(`📊 Updated sensor data from API for sensor: ${id}`);
        }

        if (signalDataResult.status === 'fulfilled' && signalDataResult.value) {
          setSignalData(signalDataResult.value);
          console.log(`📶 Updated signal data from API for sensor: ${id}`);
        }

        if (readingsDataResult.status === 'fulfilled' && readingsDataResult.value) {
          // Store raw data and filter for current timeframe
          const filteredData = filterDataByTimeframe(readingsDataResult.value, timeFrame);
          setReadings(filteredData);
          console.log(`📈 Updated readings data from API for sensor: ${id}`);
        }

        setLastUpdated(new Date());
        setIsUsingFallbackData(false); // We successfully got API data
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load sensor data';
      console.error('Error loading sensor data:', errorMessage);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
      setIsUpdatingData(false);
      setIsUpdatingSignal(false);
      setIsUpdatingReadings(false);
    }
  }, [id, fetchSensorDetails, fetchSensorData, fetchSignalData, fetchSensorReadings]);

  /**
   * Smart polling for real-time updates
   */
  const { isPolling, startPolling, stopPolling, pollNow, stats } = useSmartPolling(
    async () => {
      if (sensor?.apiId) {
        try {
          const freshData = await fetchSensorData(sensor.apiId);
          if (freshData) {
            setSensorData(freshData);
            setLastUpdated(new Date());
          }
        } catch (error) {
          console.warn('Polling failed:', error);
        }
      }
    },
    {
      interval: 30 * 60 * 1000, // Poll every 30 minutes
      maxInterval: 60 * 60 * 1000, // Max 60 minutes
      pauseOnHidden: true,
      pauseOnError: true,
      maxErrors: 3
    }
  );

  /**
   * Permission checking function - stable without getCurrentUser dependency
   */
  const checkPermission = useCallback(async () => {
    try {
      setIsLoading(true);
      const result = await checkSensorPermission(id, getCurrentUser);
      if (!result.hasPermission) {
        navigate('/user');
        return;
      }
      setHasPermission(true);
      if (result.sensorDetails) {
        setSensorName(result.sensorDetails.name);
        setRefId(result.sensorDetails.item);
        setApiId(result.sensorDetails.apiId);
      }
    } catch (error) {
      console.error('Failed to check permissions:', error);
      navigate('/user');
    } finally {
      setIsLoading(false);
    }
  }, [id, navigate]); // Remove getCurrentUser dependency

  /**
   * Fetch user full name - stable function without dependencies
   */
  const fetchUserFullName = useCallback(async () => {
    try {
      const currentUser = getCurrentUser();
      if (!currentUser) {
        navigate('/login');
        return;
      }
      try {
        const response = await api.getUserProfile();
        if (response.data) {
          setUserFullName(response.data.fullName);
          return;
        }
      } catch (error) {
        console.warn('Failed to get user profile, falling back to users endpoint');
      }
      const usersResponse = await api.getUsers();
      const userDetails = Array.isArray(usersResponse.data)
        ? usersResponse.data.find((user) => user.username === currentUser.username)
        : usersResponse.data;
      if (userDetails) {
        setUserFullName(userDetails.fullName);
      }
    } catch (error) {
      console.error('Failed to fetch user details:', error);
      setUserFullName(getCurrentUser()?.username || 'User');
    }
  }, []); // Remove dependencies to prevent infinite loop

  /**
   * Fetch server data function
   */
  const fetchServerData = useCallback(async () => {
    if (!apiId) return false;
    try {
      setIsLoadingServerData(true);
      setServerDataError(null);
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/sensordata/${apiId}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error('Server did not return JSON');
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch server data');
      }
      setServerData(result.data);
      setShowServerDataDialog(true);
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch server data';
      setServerDataError(errorMessage);
      console.error('Server data fetch error:', errorMessage);
      return false;
    } finally {
      setIsLoadingServerData(false);
    }
  }, [apiId]);

  // Permission check on mount - no immediate fallback data display
  useEffect(() => {
    const performPermissionCheck = async () => {
      setIsLoading(true);

      const timeoutId = setTimeout(() => {
        console.warn(`⏰ Permission check timeout for sensor ${id}`);
        setError('Permission check timed out');
        setIsLoading(false);
      }, 5000); // 5 second timeout

      try {
        console.log(`🔍 Checking permissions for sensor: ${id}`);

        const result = await checkSensorPermission(id, getCurrentUser);
        clearTimeout(timeoutId); // Clear timeout on success

        if (!result.hasPermission) {
          console.warn('Permission denied for sensor:', id);
          setError('You do not have permission to view this sensor');
          setIsLoading(false);
          return;
        }

        setHasPermission(true);
        if (result.sensorDetails) {
          setSensorName(result.sensorDetails.name);
          setRefId(result.sensorDetails.item);
          setApiId(result.sensorDetails.apiId);
          console.log(`✅ Loaded sensor details: ${result.sensorDetails.name}`);
        }
      } catch (error) {
        clearTimeout(timeoutId); // Clear timeout on error
        console.error('Permission check failed:', error);
        setError('Failed to check sensor permissions');
        setIsLoading(false);
      }
    };

    if (id) {
      performPermissionCheck();
    } else {
      console.error('❌ No sensor ID provided');
      navigate('/user');
    }
  }, [id]); // Only depend on id

  // Fetch user full name on mount - call directly without dependency
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const currentUser = getCurrentUser();
        if (!currentUser) {
          navigate('/login');
          return;
        }
        try {
          const response = await api.getUserProfile();
          if (response.data) {
            setUserFullName(response.data.fullName);
            return;
          }
        } catch (error) {
          console.warn('Failed to get user profile, falling back to users endpoint');
        }
        const usersResponse = await api.getUsers();
        const userDetails = Array.isArray(usersResponse.data)
          ? usersResponse.data.find((user) => user.username === currentUser.username)
          : usersResponse.data;
        if (userDetails) {
          setUserFullName(userDetails.fullName);
        }
      } catch (error) {
        console.error('Failed to fetch user details:', error);
        setUserFullName(getCurrentUser()?.username || 'User');
      }
    };

    fetchUserData();
  }, []); // Empty dependency array - run only on mount

  // Initial data load - only load API data if we have permission and no local data was shown
  useEffect(() => {
    if (hasPermission && !isUsingFallbackData) {
      loadAllData();
    } else if (hasPermission && isUsingFallbackData) {
      // We're showing local data, but we have API access - update in background
      console.log(`🔄 Updating local data with fresh API data for sensor: ${id}`);
      loadAllData().then(() => {
        console.log(`✅ Background API update completed for sensor: ${id}`);
      }).catch(err => {
        console.warn(`⚠️ Background API update failed for sensor: ${id}`, err);
      });
    }
  }, [loadAllData, hasPermission, isUsingFallbackData]);

  // Start polling when sensor is loaded - use ref to avoid dependency issues
  useEffect(() => {
    if (sensor && !isPolling) {
      console.log(`🔄 Starting 30-minute polling for sensor: ${sensor.equipmentId}`);
      startPolling();
    }

    return () => {
      console.log(`🛑 Stopping polling for sensor: ${sensor?.equipmentId}`);
      stopPolling();
    };
  }, [sensor?.equipmentId]); // Only depend on sensor ID, not the entire sensor object

  // Optimized timeframe change handling - filter cached data instead of re-fetching
  useEffect(() => {
    if (sensor && Object.keys(rawReadings).length > 0) {
      setIsLoadingTimeframe(true);

      // Use setTimeout to show loading state briefly and prevent UI blocking
      const timeoutId = setTimeout(() => {
        const filteredData = filterDataByTimeframe(rawReadings, timeFrame);
        setReadings(filteredData);
        setIsLoadingTimeframe(false);
        console.log(`📈 Filtered data for timeframe: ${timeFrame}`);
      }, 50); // Small delay to show loading state

      return () => clearTimeout(timeoutId);
    }
  }, [timeFrame, rawReadings, sensor?.equipmentId]); // Remove filterDataByTimeframe dependency

  /**
   * Helper functions matching target design
   */
  const getSignalStrengthColor = (level: number) => {
    if (level >= 80) return 'text-green-500';
    if (level >= 60) return 'text-yellow-500';
    if (level >= 40) return 'text-orange-500';
    return 'text-red-500';
  };

  const handleLogout = () => {
    navigate('/login');
  };

  const fetchData = useCallback(async () => {
    if (!id || !apiId) return;
    try {
      setIsFetching(true);
      const response = await api.getSensorData(apiId);
      const newData = {
        salinity: response.salinity,
        do: response.do,
        ph: response.ph,
        temp: response.temp,
        cond: response.cond,
        depth: response.depth,
        turbidity: response.turbidity,
        latitude: response.latitude,
        longitude: response.longitude,
        timestamp: response.timestamp
      };
      setSensorData(newData);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to fetch sensor data:', error);
    } finally {
      setIsFetching(false);
    }
  }, [apiId, id]);

  /**
   * Manual refresh function
   */
  const handleRefresh = useCallback(async () => {
    if (!id) return;

    // Invalidate all caches for this sensor
    invalidateCache(`sensor:.*:${id}`);
    if (sensor?.apiId) {
      invalidateCache(`sensor:.*:${sensor.apiId}`);
    }

    // Reload all data
    setIsLoading(true);
    await loadAllData();
    pollNow(); // Force immediate poll
  }, [id, sensor?.apiId, invalidateCache, loadAllData, pollNow]);

  /**
   * Optimized chart data with smart sampling for performance
   */
  const chartData = useMemo(() => {
    if (!readings || Object.keys(readings).length === 0) return {};

    return Object.entries(readings).reduce((acc, [param, data]) => {
      if (!data || data.length === 0) {
        acc[param] = [];
        return acc;
      }

      // Smart sampling based on data size and timeframe
      let sampledData = data;
      const maxPoints = timeFrame === '1d' ? 288 : timeFrame === '7d' ? 336 : 720; // Reasonable limits

      if (data.length > maxPoints) {
        // Sample data to reduce chart rendering load
        const step = Math.ceil(data.length / maxPoints);
        sampledData = data.filter((_, index) => index % step === 0);
        console.log(`📉 Sampled ${param} data: ${data.length} -> ${sampledData.length} points`);
      }

      acc[param] = sampledData;
      return acc;
    }, {} as Record<string, SensorReading[]>);
  }, [readings, timeFrame]);

  /**
   * Performance stats for debugging
   */

  // Error state - show if an error occurred during data loading
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-4 bg-white shadow-md rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-red-500 mx-auto mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="text-xl text-red-600">Error Loading Sensor Data</p>
          <p className="mt-2 text-gray-700">We encountered an issue while trying to load the sensor data. Details:</p>
          <p className="mt-1 text-sm text-gray-500 bg-gray-100 p-2 rounded">{error}</p>
          <p className="mt-3 text-gray-600">Please try refreshing the page or check back later. If the problem persists, you can try using fallback data if available or contact support.</p>
          <button
            onClick={() => window.location.reload()} // Simple refresh
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 mr-2"
          >
            Refresh Page
          </button>
          <button
            onClick={() => navigate('/user')}
            className="mt-4 px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
          >
            Back to Sensor List
          </button>
        </div>
      </div>
    );
  }

  // Show loading state when checking permissions or loading data
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading sensor details...</p>
        </div>
      </div>
    );
  }

  // Permission check - matching target design
  if (!hasPermission) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-xl text-gray-600">You don't have permission to view this sensor.</p>
          <button
            onClick={() => navigate('/user')}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Back to Sensor List
          </button>
        </div>
      </div>
    );
  }

  // Button section matching target design
  const buttonSection = (
    <div className="flex space-x-2 sm:space-x-4">
      <button
        onClick={async () => {
          setIsFetchingData(true);
          await fetchData();
          setIsFetchingData(false);
        }}
        disabled={isFetchingServerData}
        className={`flex items-center justify-center space-x-1 sm:space-x-2 px-2 sm:px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 relative min-w-[40px] ${
          isFetchingData || isFetching ? 'opacity-75 cursor-not-allowed' : ''
        }`}
        title="Refresh Data"
      >
        {!(isFetchingData || isFetching) ? (
          <>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="w-5 h-5"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.3" />
            </svg>
            <span className="hidden sm:inline">Refresh Data</span>
          </>
        ) : (
          <div className="w-5 h-5 border-t-2 border-b-2 border-white rounded-full animate-spin" />
        )}
      </button>
      <button
        onClick={async () => {
          setShowServerDataDialog(true);
          setIsFetchingServerData(true);
          const success = await fetchServerData();
          if (!success) {
            console.error('Failed to fetch server data after retry');
          }
          setIsFetchingServerData(false);
        }}
        disabled={isFetchingData}
        className={`flex items-center justify-center space-x-1 sm:space-x-2 px-2 sm:px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 relative min-w-[40px] ${
          isFetchingServerData ? 'opacity-75 cursor-not-allowed' : ''
        }`}
        title="Server Data"
      >
        {!isFetchingServerData ? (
          <>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="w-5 h-5"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <rect x="2" y="2" width="20" height="8" rx="2" ry="2" />
              <rect x="2" y="14" width="20" height="8" rx="2" ry="2" />
              <line x1="6" y1="6" x2="6.01" y2="6" />
              <line x1="6" y1="18" x2="6.01" y2="18" />
            </svg>
            <span className="hidden sm:inline">Server Data</span>
          </>
        ) : (
          <div className="w-5 h-5 border-t-2 border-b-2 border-white rounded-full animate-spin" />
        )}
      </button>
      <button
        onClick={() => setShowReportDialog(true)}
        className="flex items-center justify-center space-x-1 sm:space-x-2 px-2 sm:px-4 py-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors duration-200 min-w-[40px]"
        title="Historical Reports"
      >
        <FileText className="w-5 h-5" />
        <span className="hidden sm:inline">Historical Reports</span>
      </button>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Header
        userFullName={userFullName}
        onLogout={handleLogout}
        showBackButton={true}
        onBack={() => navigate('/user')}
      />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center space-x-4">
            <h2 className="text-2xl font-bold text-gray-900">Sensor Details</h2>
          </div>
          {buttonSection}
        </div>

        {/* Fallback Data Notification */}
        {isUsingFallbackData && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 rounded-r-lg">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  <strong>Using Local Data:</strong> API connection unavailable. Displaying cached sensor data for demonstration purposes.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Sensor Information Section - matching target design */}
        <div className="bg-white rounded-lg p-6 shadow-md mb-8 relative">
          {isUpdatingSignal && (
            <div className="absolute top-2 right-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <div className="text-sm text-gray-500">Sensor Name</div>
                <div className="text-lg font-semibold">{sensorName || `Sensor ${id}`}</div>
              </div>
              <div className="flex space-x-8">
                <div>
                  <div className="text-sm text-gray-500">Sensor ID</div>
                  <div className="text-lg font-semibold">{id}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Ref ID</div>
                  <div className="text-lg font-semibold">{refId}</div>
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500 flex items-center space-x-2">
                  <span>Status</span>
                  {isUpdatingSignal && <span className="text-xs text-blue-600">Updating...</span>}
                </div>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    signalData?.status === 'online' ? 'bg-green-500' : 'bg-red-500'
                  }`} />
                  <span className="text-lg font-semibold capitalize">{signalData?.status}</span>
                </div>
              </div>
            </div>
            <div className="flex flex-col items-end justify-center">
              <div className="flex items-center space-x-2">
                <span className="text-lg font-semibold">
                  {signalData?.signalLevel}%
                </span>
                <Signal className={`w-5 h-5 ${getSignalStrengthColor(signalData?.signalLevel || 0)}`} />
              </div>
            </div>
          </div>
          <div className="mt-4 flex items-center space-x-2 text-gray-500">
            <MapPin className="w-4 h-4" />
            <span className="text-sm">
              Lat: {sensorData?.latitude}, Long: {sensorData?.longitude}
            </span>
          </div>
        </div>

        {/* Current readings grid - matching target design */}
        {sensorData ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <ReadingCard
              title="Salinity"
              value={sensorData.salinity || 0}
              unit="ppt"
              timestamp={sensorData.timestamp}
              icon={<Droplet className="w-5 h-5 text-blue-500" />}
              isUpdating={isUpdatingData}
            />
            <ReadingCard
              title="DO"
              value={sensorData.do || 0}
              unit="mg/L"
              timestamp={sensorData.timestamp}
              icon={<Wind className="w-5 h-5 text-green-500" />}
              isUpdating={isUpdatingData}
            />
            <ReadingCard
              title="pH"
              value={sensorData.ph || 0}
              unit="pH"
              timestamp={sensorData.timestamp}
              icon={<Activity className="w-5 h-5 text-purple-500" />}
              isUpdating={isUpdatingData}
            />
            <ReadingCard
              title="Temperature"
              value={sensorData.temp || 0}
              unit="deg.C"
              timestamp={sensorData.timestamp}
              icon={<Thermometer className="w-5 h-5 text-red-500" />}
              isUpdating={isUpdatingData}
            />
            <ReadingCard
              title="Conductivity"
              value={sensorData.cond || 0}
              unit="mS/cm"
              timestamp={sensorData.timestamp}
              icon={<Zap className="w-5 h-5 text-cyan-500" />}
              isUpdating={isUpdatingData}
            />
            <ReadingCard
              title="Depth"
              value={sensorData.depth || 0}
              unit="m"
              timestamp={sensorData.timestamp}
              icon={<Anchor className="w-5 h-5 text-indigo-500" />}
              isUpdating={isUpdatingData}
            />
            <ReadingCard
              title="Turbidity"
              value={sensorData.turbidity || 0}
              unit="NTU"
              timestamp={sensorData.timestamp}
              icon={<Cloud className="w-5 h-5 text-red-700" />}
              isUpdating={isUpdatingData}
            />
          </div>
        ) : (
          <div className="bg-gray-50 rounded-lg p-8 text-center mb-8">
            <div className="text-gray-500 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-700">No Sensor Data Available</h3>
              <p className="mt-2 text-sm text-gray-500">
                We couldn't retrieve any data for this sensor. Please try refreshing the page or check back later.
              </p>
            </div>
            <button
              onClick={handleRefresh}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Refresh Data
            </button>
          </div>
        )}

        {/* Historical Data View Section - matching target design */}
        <div className="mb-6 flex justify-between items-center">
          <div className="text-gray-600">
            <h3 className="text-lg font-medium">Historical Data View</h3>
            <p className="text-sm">
              Select time range to view historical sensor readings
              {isLoadingTimeframe && (
                <span className="ml-2 text-blue-600 text-xs">
                  🔄 Updating charts...
                </span>
              )}
            </p>
          </div>
          <div className="relative">
            <TimeFrameToggle
              timeFrame={timeFrame}
              onChange={(newTimeFrame) => {
                setTimeFrame(newTimeFrame);
              }}
            />
            {isLoadingTimeframe && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
            )}
          </div>
        </div>

        {/* Charts Section - matching target design exactly */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <SensorChart
              data={chartData?.salinity || []}
              title="Salinity"
              unit="ppt"
              color="#2563eb"
              timeFrame={timeFrame}
              isLoading={isLoadingTimeframe || isUpdatingReadings}
            />
          </div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <SensorChart
              data={chartData?.do || []}
              title="DO"
              unit="mg/L"
              color="#16a34a"
              timeFrame={timeFrame}
              isLoading={isLoadingTimeframe || isUpdatingReadings}
            />
          </div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <SensorChart
              data={chartData?.ph || []}
              title="pH"
              unit="pH"
              color="#9333ea"
              timeFrame={timeFrame}
              isLoading={isLoadingTimeframe || isUpdatingReadings}
            />
          </div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <SensorChart
              data={chartData?.temp || []}
              title="Temperature"
              unit="deg.C"
              color="#dc2626"
              timeFrame={timeFrame}
              isLoading={isLoadingTimeframe || isUpdatingReadings}
            />
          </div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <SensorChart
              data={chartData?.cond || []}
              title="Conductivity"
              unit="mS/cm"
              color="#0891b2"
              timeFrame={timeFrame}
              isLoading={isLoadingTimeframe || isUpdatingReadings}
            />
          </div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <SensorChart
              data={chartData?.depth || []}
              title="Depth"
              unit="m"
              color="#4f46e5"
              timeFrame={timeFrame}
              isLoading={isLoadingTimeframe || isUpdatingReadings}
            />
          </div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <SensorChart
              data={chartData?.turbidity || []}
              title="Turbidity"
              unit="NTU"
              color="#b91c1c"
              timeFrame={timeFrame}
              isLoading={isLoadingTimeframe || isUpdatingReadings}
            />
          </div>
        </div>
      </main>

        {/* Dialogs - matching target design */}
        {showFetchDialog && (
          <FetchDataDialog
            onClose={() => setShowFetchDialog(false)}
            onDataUpdate={(newData) => {
              setSensorData((prev) => ({ ...prev, ...newData }));
              setLastUpdated(new Date());
            }}
            currentValues={{
              salinity: sensorData?.salinity || 0,
              do: sensorData?.do || 0,
              ph: sensorData?.ph || 0,
              temp: sensorData?.temp || 0,
              cond: sensorData?.cond || 0,
              depth: sensorData?.depth || 0,
              turbidity: sensorData?.turbidity || 0,
              latitude: sensorData?.latitude || 0,
              longitude: sensorData?.longitude || 0,
              equipmentId: apiId
            }}
          />
        )}
        {showReportDialog && (
          <HistoricalReportDialog
            sensorId={id || ''}
            onClose={() => setShowReportDialog(false)}
          />
        )}
        {showServerDataDialog && (
          <ServerDataDialog
            isOpen={showServerDataDialog}
            onClose={() => setShowServerDataDialog(false)}
            data={serverData}
            isLoading={isLoadingServerData}
            error={serverDataError}
          />
        )}

      {/* Performance Monitor (development only) */}
      <PerformanceMonitor componentName="OptimizedSensorDetail" />
    </div>
  );
}

// Component for displaying individual sensor readings
interface ReadingCardProps {
  title: string;
  value: number;
  unit: string;
  timestamp: string;
  icon: React.ReactNode;
  isUpdating?: boolean;
}

const ReadingCard: React.FC<ReadingCardProps> = ({ title, value, unit, timestamp, icon, isUpdating = false }) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md relative">
      {isUpdating && (
        <div className="absolute top-2 right-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        </div>
      )}

      <div className="flex items-center space-x-2 mb-1">
        {icon}
        <h3 className="text-sm font-medium text-gray-500">{title}</h3>
        {isUpdating && (
          <span className="text-xs text-blue-600">Updating...</span>
        )}
      </div>
      <div className="flex items-baseline space-x-2">
        <span className="text-2xl font-bold text-gray-900">{value.toFixed(2)}</span>
        <span className="text-sm text-gray-500">{unit}</span>
      </div>
      <div className="mt-2">
        <span className="text-xs text-gray-400">{formatDateTime(timestamp, true)}</span>
      </div>
    </div>
  );
};

export default OptimizedSensorDetailPage;
