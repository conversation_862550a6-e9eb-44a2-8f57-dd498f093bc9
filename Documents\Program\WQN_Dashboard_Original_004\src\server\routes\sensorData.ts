import express from 'express';
import fetch from 'node-fetch';
import cors from 'cors';
import { authMiddleware } from '../middleware/auth';
import dotenv from 'dotenv';
import { processApiTimestamp } from '../../utils/dateFormat'; // Import the processApiTimestamp function
import { cache, CACHE_KEYS, CACHE_TTL, cacheUtils } from '../utils/cache';
import { createPerformanceMonitor } from '../config/performance';
import {
  getSensorParameterMapping,
  getDefaultParameterMapping,
  extractSensorData,
  validateSensorData
} from '../config/sensorParameterMapping';

dotenv.config();

const performanceMonitor = createPerformanceMonitor();

const router = express.Router();

router.use(cors({
  origin: 'http://localhost:5173',
  credentials: true
}));

interface TokenResponse {
  data: {
    access_token: string;
  };
}

async function getToken(): Promise<string> {
  // Try to get token from cache first
  const cachedToken = cache.get<string>(CACHE_KEYS.API_TOKEN);
  if (cachedToken) {
    console.log('Using cached API token');
    return cachedToken;
  }

  console.log('Fetching new API token');
  const perfId = 'api_token_fetch';
  performanceMonitor.start(perfId);

  try {
    const tokenUrl = process.env.VITE_TOKEN_URL!;
    const response = await fetch(tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        grant_type: "client_credentials",
        appKey: process.env.VITE_API_KEY!,
        appSecret: process.env.VITE_API_SECRET!,
        account: process.env.VITE_ACCOUNT!
      })
    });

    if (!response.ok) {
      throw new Error(`Token request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json() as TokenResponse;
    const token = data.data.access_token;

    // Cache the token for 50 minutes (tokens expire in 1 hour)
    cache.set(CACHE_KEYS.API_TOKEN, token, CACHE_TTL.API_TOKEN);

    const duration = performanceMonitor.end(perfId);
    console.log(`API token fetched in ${duration.toFixed(2)}ms`);

    return token;
  } catch (error) {
    performanceMonitor.end(perfId);
    console.error('Failed to fetch API token:', error);
    throw error;
  }
}

router.get('/sensordata/:apiId', authMiddleware, async (req, res) => {
  const { apiId } = req.params;
  const cacheKey = CACHE_KEYS.SENSOR_DATA(apiId);
  const perfId = `sensor_data_${apiId}`;

  try {
    // Check cache first
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Cache hit for sensor data: ${apiId}`);
      return res.json(cachedData);
    }

    console.log(`Cache miss for sensor data: ${apiId}`);
    performanceMonitor.start(perfId);

    const token = await getToken();

    // Enable monitoring mode
    const monitorUrl = process.env.VITE_MONITOR_OPEN_URL!;
    const monitorResponse = await fetch(monitorUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({ equipmentId: apiId })
    });

    if (!monitorResponse.ok) {
      throw new Error(`Failed to enable monitoring mode: ${monitorResponse.status}`);
    }

    // Fetch sensor data
    const sensorUrl = process.env.VITE_SENSOR_DATA_URL!;
    const sensorResponse = await fetch(sensorUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({ equipmentId: apiId })
    });

    const data = await sensorResponse.json();
    if (!sensorResponse.ok || data.code !== '200') {
      throw new Error(data.message || 'Failed to fetch sensor data');
    }

    const sensorData = data.data;

    // Get parameter mapping for this sensor
    const parameterMapping = getSensorParameterMapping(apiId);

    // Validate sensor data before processing
    const validation = validateSensorData(sensorData, parameterMapping);
    if (!validation.isValid) {
      console.warn(`Missing sensor parameters for ${apiId}:`, validation.missingParameters);
      console.log(`Available fields:`, validation.availableFields);
    }

    // Extract sensor data using dynamic parameter mapping
    const extractedData = extractSensorData(sensorData, parameterMapping);

    const responseData = {
      success: true,
      data: {
        ...extractedData,
        cached: false
      }
    };

    // Cache the response for 1 minute
    cache.set(cacheKey, { ...responseData, data: { ...responseData.data, cached: true } }, CACHE_TTL.SENSOR_DATA);

    const duration = performanceMonitor.end(perfId);
    console.log(`Sensor data fetched for ${apiId} in ${duration.toFixed(2)}ms`);

    res.json(responseData);
  } catch (error) {
    performanceMonitor.end(perfId);
    console.error(`Error fetching sensor data for ${apiId}:`, error);

    // Log additional context for debugging
    console.error(`Sensor ID: ${apiId}`);
    console.error(`Parameter mapping used:`, getSensorParameterMapping(apiId));

    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      apiId,
      context: {
        sensorId: apiId,
        parameterMapping: getSensorParameterMapping(apiId)
      }
    });
  }
});

export default router;