import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { optimizedApiService } from '../services/optimizedApiService';
import { useApiOptimization } from '../hooks/useApiOptimization';
import { useSmartPolling } from '../hooks/useSmartPolling';
import { Sensor } from '../types';
import { processApiTimestamp } from '../utils/dateFormat';

/**
 * Optimized SensorDetail component with intelligent caching and polling
 */
const OptimizedSensorDetail: React.FC = () => {
  const { equipmentId } = useParams<{ equipmentId: string }>();
  const navigate = useNavigate();
  
  const [sensor, setSensor] = useState<Sensor | null>(null);
  const [sensorData, setSensorData] = useState<any>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const { optimizedCall, invalidateCache, preload } = useApiOptimization();

  /**
   * Fetch sensor details with caching
   */
  const fetchSensorDetails = useCallback(async () => {
    if (!equipmentId) return;

    try {
      const sensorDetails = await optimizedCall(
        `sensor:details:${equipmentId}`,
        () => optimizedApiService.getSensorDetails(equipmentId),
        { ttl: 30 * 60 * 1000 } // 30 minutes cache
      );
      setSensor(sensorDetails);
    } catch (err) {
      console.error('Failed to fetch sensor details:', err);
      setError('Failed to load sensor details');
    }
  }, [equipmentId, optimizedCall]);

  /**
   * Fetch sensor data with smart caching
   */
  const fetchSensorData = useCallback(async () => {
    if (!sensor?.apiId) return;

    try {
      const data = await optimizedCall(
        `sensor:data:${sensor.apiId}`,
        () => optimizedApiService.getSensorData(sensor.apiId),
        { 
          ttl: 2 * 60 * 1000, // 2 minutes cache for data
          staleWhileRevalidate: true // Return stale data while fetching fresh
        }
      );
      
      if (data) {
        setSensorData(data);
        setLastUpdated(new Date());
        setError(null);
      }
    } catch (err) {
      console.error('Failed to fetch sensor data:', err);
      setError('Failed to load sensor data');
    }
  }, [sensor?.apiId, optimizedCall]);

  /**
   * Smart polling for sensor data
   */
  const { 
    isPolling, 
    startPolling, 
    stopPolling, 
    pollNow,
    stats: pollingStats 
  } = useSmartPolling(
    fetchSensorData,
    {
      interval: 5 * 60 * 1000, // 5 minutes base interval
      maxInterval: 15 * 60 * 1000, // Max 15 minutes
      backoffMultiplier: 1.5,
      pauseOnHidden: true,
      pauseOnError: true,
      maxErrors: 3,
      onError: (error) => {
        console.error('Polling error:', error);
        setError(`Polling failed: ${error.message}`);
      },
      onSuccess: () => {
        setError(null);
      }
    }
  );

  /**
   * Initialize component
   */
  useEffect(() => {
    fetchSensorDetails();
  }, [fetchSensorDetails]);

  /**
   * Start polling when sensor is loaded
   */
  useEffect(() => {
    if (sensor?.apiId) {
      fetchSensorData(); // Initial fetch
      startPolling(); // Start polling
    }

    return () => {
      stopPolling(); // Cleanup on unmount
    };
  }, [sensor?.apiId, fetchSensorData, startPolling, stopPolling]);

  /**
   * Preload related data
   */
  useEffect(() => {
    if (sensor) {
      // Preload user sensors list for better navigation
      preload(
        'user:sensors',
        () => optimizedApiService.getUserSensors(),
        { ttl: 10 * 60 * 1000 }
      );
    }
  }, [sensor, preload]);



  /**
   * Format sensor data for display
   */
  const formattedData = useMemo(() => {
    if (!sensorData?.data) return null;

    return sensorData.data.map((item: any) => ({
      ...item,
      timestamp: processApiTimestamp(item.timestamp),
      formattedValue: parseFloat(item.value).toFixed(2)
    }));
  }, [sensorData]);

  /**
   * Get status indicator
   */
  const statusIndicator = useMemo(() => {
    if (error) return { color: 'red', text: 'Error' };
    if (!isPolling) return { color: 'orange', text: 'Paused' };
    if (pollingStats.isExecuting) return { color: 'blue', text: 'Updating...' };
    return { color: 'green', text: 'Live' };
  }, [error, isPolling, pollingStats.isExecuting]);

  if (!equipmentId) {
    return <div className="error">Invalid sensor ID</div>;
  }

  if (!sensor) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading sensor details...</p>
      </div>
    );
  }

  return (
    <div className="sensor-detail-container">
      {/* Header */}
      <div className="sensor-header">
        <button 
          onClick={() => navigate('/user')} 
          className="back-button"
        >
          ← Back to Dashboard
        </button>
        
        <div className="sensor-title">
          <h1>{sensor.name}</h1>
          <div className="sensor-meta">
            <span className="location">📍 {sensor.location}</span>
            <span className="equipment-id">ID: {sensor.equipmentId}</span>
          </div>
        </div>

        <div className="sensor-controls">
          <div className="status-indicator">
            <span
              className="status-dot"
              style={{ backgroundColor: statusIndicator.color }}
            ></span>
            {statusIndicator.text}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="error-banner">
          ⚠️ {error}
          <button onClick={handleRefresh} className="retry-button">
            Retry
          </button>
        </div>
      )}

      {/* Sensor Info */}
      <div className="sensor-info-grid">
        <div className="info-card">
          <h3>Sensor Information</h3>
          <div className="info-row">
            <span>Type:</span>
            <span>{sensor.item}</span>
          </div>
          <div className="info-row">
            <span>Status:</span>
            <span className={`status ${sensor.status?.toLowerCase()}`}>
              {sensor.status}
            </span>
          </div>
          <div className="info-row">
            <span>API ID:</span>
            <span>{sensor.apiId}</span>
          </div>
        </div>

        <div className="info-card">
          <h3>Data Status</h3>
          <div className="info-row">
            <span>Last Updated:</span>
            <span>{lastUpdated?.toLocaleString() || 'Never'}</span>
          </div>
          <div className="info-row">
            <span>Polling Interval:</span>
            <span>{Math.round(pollingStats.interval / 1000)}s</span>
          </div>
          <div className="info-row">
            <span>Error Count:</span>
            <span>{pollingStats.errors}/{pollingStats.maxErrors}</span>
          </div>
        </div>
      </div>

      {/* Sensor Data */}
      <div className="sensor-data-section">
        <h2>Recent Data</h2>
        
        {formattedData && formattedData.length > 0 ? (
          <div className="data-table-container">
            <table className="data-table">
              <thead>
                <tr>
                  <th>Timestamp</th>
                  <th>Value</th>
                  <th>Unit</th>
                </tr>
              </thead>
              <tbody>
                {formattedData.slice(0, 20).map((item: any, index: number) => (
                  <tr key={index}>
                    <td>{item.timestamp}</td>
                    <td className="value">{item.formattedValue}</td>
                    <td>{item.unit || sensor.item}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="no-data">
            <p>No recent data available</p>
            <button onClick={pollNow} className="fetch-button">
              Fetch Data
            </button>
          </div>
        )}
      </div>

      {/* Debug Info (Development only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="debug-info">
          <h3>Debug Information</h3>
          <pre>{JSON.stringify({
            pollingStats,
            cacheStats: 'Available via getCacheStats()',
            lastUpdated: lastUpdated?.toISOString()
          }, null, 2)}</pre>
        </div>
      )}
    </div>
  );
};

export default OptimizedSensorDetail;
