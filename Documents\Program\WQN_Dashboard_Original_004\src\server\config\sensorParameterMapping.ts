/**
 * Sensor Parameter Mapping Configuration
 * 
 * This file defines the mapping between sensor parameters and their corresponding
 * API field IDs. This allows for dynamic configuration of different sensor types
 * and removes hardcoded parameter IDs from the codebase.
 */

export interface SensorParameterMapping {
  salinity: string;
  do: string;
  ph: string;
  temp: string;
  cond: string;
  depth: string;
  turbidity: string;
  latitude: string;
  longitude: string;
  timestampField: string; // Field to use for timestamp extraction
}

export interface SensorConfig {
  equipmentId: string;
  apiId: string;
  name: string;
  sensorType: string;
  parameterMapping: SensorParameterMapping;
}

/**
 * Default parameter mapping for standard water quality sensors
 * This is used as a fallback when no specific mapping is found
 */
export const DEFAULT_PARAMETER_MAPPING: SensorParameterMapping = {
  salinity: '2057461',
  do: '2057459',
  ph: '2057457',
  temp: '2057456',
  cond: '2057455',
  depth: '2057454',
  turbidity: '2057453',
  latitude: '2057492',
  longitude: '2057491',
  timestampField: '2057461' // Use salinity field for timestamp by default
};

/**
 * Sensor-specific parameter mappings
 * Add new sensor configurations here as needed
 */
export const SENSOR_PARAMETER_MAPPINGS: Record<string, SensorParameterMapping> = {
  // Default mapping for most sensors
  'default': DEFAULT_PARAMETER_MAPPING,
  
  // Equipment-specific mappings (can be added as needed)
  'NB001': DEFAULT_PARAMETER_MAPPING,
  'NB002': DEFAULT_PARAMETER_MAPPING,
  'NB003': DEFAULT_PARAMETER_MAPPING,
  
  // API ID specific mappings
  '22063': DEFAULT_PARAMETER_MAPPING,
  
  // Example of a different sensor type with different parameter IDs
  // 'SENSOR_TYPE_2': {
  //   salinity: '3057461',
  //   do: '3057459',
  //   ph: '3057457',
  //   temp: '3057456',
  //   cond: '3057455',
  //   depth: '3057454',
  //   turbidity: '3057453',
  //   latitude: '3057492',
  //   longitude: '3057491',
  //   timestampField: '3057461'
  // }
};

/**
 * Get parameter mapping for a specific sensor
 * @param sensorId - Equipment ID or API ID of the sensor
 * @returns Parameter mapping configuration for the sensor
 */
export function getSensorParameterMapping(sensorId: string): SensorParameterMapping {
  // First try to find exact match by sensor ID
  if (SENSOR_PARAMETER_MAPPINGS[sensorId]) {
    return SENSOR_PARAMETER_MAPPINGS[sensorId];
  }
  
  // If no specific mapping found, use default
  return DEFAULT_PARAMETER_MAPPING;
}

/**
 * Get default parameter mapping
 * @returns Default parameter mapping configuration
 */
export function getDefaultParameterMapping(): SensorParameterMapping {
  return DEFAULT_PARAMETER_MAPPING;
}

/**
 * Extract sensor data using parameter mapping
 * @param apiData - Raw API response data
 * @param mapping - Parameter mapping configuration
 * @returns Structured sensor data object
 */
export function extractSensorData(apiData: any, mapping: SensorParameterMapping) {
  return {
    salinity: parseFloat(apiData[mapping.salinity]?.value) || 0,
    do: parseFloat(apiData[mapping.do]?.value) || 0,
    ph: parseFloat(apiData[mapping.ph]?.value) || 0,
    temp: parseFloat(apiData[mapping.temp]?.value) || 0,
    cond: parseFloat(apiData[mapping.cond]?.value) || 0,
    depth: parseFloat(apiData[mapping.depth]?.value) || 0,
    turbidity: parseFloat(apiData[mapping.turbidity]?.value) || 0,
    latitude: parseFloat(apiData[mapping.latitude]?.value) || 0,
    longitude: parseFloat(apiData[mapping.longitude]?.value) || 0,
    timestamp: apiData[mapping.timestampField]?.time ? 
      processApiTimestamp(apiData[mapping.timestampField].time) : 
      processApiTimestamp(new Date().toISOString())
  };
}

/**
 * Validate that all required parameters are present in the API response
 * @param apiData - Raw API response data
 * @param mapping - Parameter mapping configuration
 * @returns Object with validation results and missing parameters
 */
export function validateSensorData(apiData: any, mapping: SensorParameterMapping) {
  const missingParameters: string[] = [];
  const requiredParams = [
    'salinity', 'do', 'ph', 'temp', 'cond', 'depth', 'turbidity', 'latitude', 'longitude'
  ];
  
  requiredParams.forEach(param => {
    const fieldId = mapping[param as keyof SensorParameterMapping];
    if (!apiData[fieldId] || apiData[fieldId].value === undefined) {
      missingParameters.push(param);
    }
  });
  
  return {
    isValid: missingParameters.length === 0,
    missingParameters,
    availableFields: Object.keys(apiData)
  };
}

/**
 * Add or update sensor parameter mapping
 * @param sensorId - Equipment ID or API ID of the sensor
 * @param mapping - Parameter mapping configuration
 */
export function setSensorParameterMapping(sensorId: string, mapping: SensorParameterMapping): void {
  SENSOR_PARAMETER_MAPPINGS[sensorId] = mapping;
}

/**
 * Get all available sensor parameter mappings
 * @returns Record of all sensor parameter mappings
 */
export function getAllSensorParameterMappings(): Record<string, SensorParameterMapping> {
  return { ...SENSOR_PARAMETER_MAPPINGS };
}
